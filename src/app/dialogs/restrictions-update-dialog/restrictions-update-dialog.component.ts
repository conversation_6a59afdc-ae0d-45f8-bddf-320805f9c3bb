import {Component, Inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {RestrictionsUpdate} from '../../data/pricing';
import {RestrictionsUpdateFormComponent} from '../../forms/restrictions-update-form/restrictions-update-form.component';
import {PricingService} from '../../services/pricing.service';
import {inject} from '@angular/core';

export interface RestrictionsUpdateDialogInput {
   data?: RestrictionsUpdate;
}

@Component({
   selector: 'app-restrictions-update-dialog',
   templateUrl: './restrictions-update-dialog.component.html',
   standalone: false
})
export class RestrictionsUpdateDialogComponent {
   @ViewChild(RestrictionsUpdateFormComponent) form!: RestrictionsUpdateFormComponent;

   private sPricing = inject(PricingService);

   constructor(
      @Inject(MAT_DIALOG_DATA) public input: RestrictionsUpdateDialogInput,
      private dialogRef: MatDialogRef<RestrictionsUpdateDialogComponent>
   ) {
   }

   get windowTitle(): string {
      return 'Актуализация на ограничения';
   }

   trySubmit(): void {
      if (this.form.valid) {
         const restrictionsUpdate = this.form.value;
         
         this.sPricing.submitRestrictionsUpdate(restrictionsUpdate).subscribe({
            next: () => {
               this.dialogRef.close(true);
            },
            error: (error) => {
               console.error('Error submitting restrictions update:', error);
               // You might want to show an error message to the user here
            }
         });
      }
   }

   cancel(): void {
      this.dialogRef.close(false);
   }
}
