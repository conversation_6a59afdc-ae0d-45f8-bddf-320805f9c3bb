<div class="settings-header">
   <div class="description">
      <h1>Сезонни цени</h1>
      <h3><i>
         Сезонни цени, които ще определят цената на пакетите спрямо датата на
         резервацията.
      </i></h3>
   </div>

   <button (click)="addPricing()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нов ценоразпис
   </button>

   <button (click)="openRestrictionsUpdate()" color="accent" mat-raised-button>
      <mat-icon>settings</mat-icon>
      Актуализация на ограничения
   </button>
</div>

@for (data of pricingData$ | async; track data.pricing.id) {
   <div class="pricing-list">
      <app-pricing-reference (deletePricing)="deletePricing($event)"
                             (editPricing)="editPricing($event)"
                             [pricing]="data.pricing"/>
   </div>
}
