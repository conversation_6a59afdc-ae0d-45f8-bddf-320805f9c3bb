import {ID, Identifiable} from './identifiable';
import {DateRange, DayOfWeek, Money} from './common';

export interface PricingRange {
   dateRange: DateRange;
   priority: number;
   activeWeekDays?: DayOfWeek[];
}

export interface Pricing extends Identifiable {
   name: string;
   ranges: PricingRange[];
   bundlePrices: Record<ID, Money>;
}

export interface Restrictions {
   rate?: number;
   minStayArrival?: number;
   minStayThrough?: number;
   maxStay?: number;
   closedToArrival?: boolean;
   closedToDeparture?: boolean;
   stopSell?: boolean;
}

export interface PlanRestrictionsUpdate {
   ratePlanId: ID;
   restrictions: Restrictions;
}

export interface RestrictionsUpdateRange {
   range: PricingRange;
   updates: PlanRestrictionsUpdate[];
}

export interface RestrictionsUpdate {
   ranges: RestrictionsUpdateRange[];
}
