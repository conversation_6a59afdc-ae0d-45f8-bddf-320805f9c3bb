import {AfterViewInit, Component, inject, Input, OnInit, ViewChild} from '@angular/core';
import {RestrictionsUpdate, RestrictionsUpdateRange, PlanRestrictionsUpdate, Restrictions, PricingRange} from '../../data/pricing';
import {
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {ID} from '../../data/identifiable';
import {DateTime} from 'luxon';
import {MatDateRangePicker} from '@angular/material/datepicker';
import {PricingService} from 'src/app/services/pricing.service';
import {Price} from 'src/app/data/price';
import {DateRange, DayOfWeek} from '../../data/common';
import {cmpName} from '../../utility/utility';

@Component({
   selector: 'app-restrictions-update-form',
   templateUrl: './restrictions-update-form.component.html',
   styleUrls: ['./restrictions-update-form.component.scss'],
   standalone: false
})
export class RestrictionsUpdateFormComponent implements OnInit, AfterViewInit {
   @ViewChild(MatDateRangePicker) datePicker!: MatDateRangePicker<DateTime>;

   @Input() data?: RestrictionsUpdate;

   form = this.fb.group({
      ranges: this.fb.array([])
   });
   
   prices: Price[] = [];
   days = DayOfWeek;

   start = this.fb.control(null);
   end = this.fb.control(null);

   private sPricing = inject(PricingService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): RestrictionsUpdate {
      return {
         ranges: this.ranges.value.map((rangeValue: any) => ({
            range: {
               dateRange: rangeValue.dateRange,
               priority: rangeValue.priority,
               activeWeekDays: rangeValue.enableWeekDays ? rangeValue.activeWeekDays : undefined
            },
            updates: rangeValue.updates
         }))
      };
   }

   get ranges(): UntypedFormArray {
      return this.form.get('ranges') as UntypedFormArray;
   }

   ngOnInit(): void {
      this.sPricing.getAllPrices().subscribe(prices => {
         this.prices = prices.sort(cmpName);

         if (this.data) {
            this.data.ranges.forEach(r => this.addRange(r));
         }
      });
   }

   ngAfterViewInit(): void {
      this.datePicker.openedStream.subscribe(() => {
         this.start.setValue(null);
         this.end.setValue(null);
      });

      this.datePicker.closedStream.subscribe(() => {
         if (this.start.value && this.end.value) {
            const dateRange = {start: this.start.value, end: this.end.value};
            const pricingRange = {dateRange, priority: 0} as PricingRange;
            const restrictionsUpdateRange: RestrictionsUpdateRange = {
               range: pricingRange,
               updates: []
            };
            this.addRange(restrictionsUpdateRange);
         }
      });
   }

   getDateRange(index: number): DateRange {
      return this.ranges.at(index)?.get('dateRange')?.value as DateRange;
   }

   getUpdatesArray(rangeIndex: number): UntypedFormArray {
      return this.ranges.at(rangeIndex)?.get('updates') as UntypedFormArray;
   }

   addPlanUpdate(rangeIndex: number): void {
      const updatesArray = this.getUpdatesArray(rangeIndex);
      const planUpdateGroup = this.createPlanUpdateGroup();
      updatesArray.push(planUpdateGroup);
   }

   removePlanUpdate(rangeIndex: number, updateIndex: number): void {
      const updatesArray = this.getUpdatesArray(rangeIndex);
      updatesArray.removeAt(updateIndex);
   }

   originalOrder(_: any, __: any) {
      return 0;
   }

   private addRange(range: RestrictionsUpdateRange): void {
      const rangeGroup = this.fb.group({
         dateRange: this.fb.group({
            start: [range.range.dateRange.start, Validators.required],
            end: [range.range.dateRange.end, Validators.required]
         }),
         enableWeekDays: this.fb.control(false),
         activeWeekDays: this.fb.control(range.range.activeWeekDays),
         priority: this.fb.control(range.range.priority),
         updates: this.fb.array([])
      });

      rangeGroup.get('enableWeekDays')?.valueChanges
         .subscribe(newValue => this.setWeekDaysDisabledState(newValue, rangeGroup));
      rangeGroup.patchValue({
         enableWeekDays: range.range.activeWeekDays !== undefined
      });

      // Add existing plan updates
      const updatesArray = rangeGroup.get('updates') as UntypedFormArray;
      range.updates.forEach(update => {
         const planUpdateGroup = this.createPlanUpdateGroup(update);
         updatesArray.push(planUpdateGroup);
      });

      this.ranges.push(rangeGroup);
   }

   private createPlanUpdateGroup(update?: PlanRestrictionsUpdate): UntypedFormGroup {
      return this.fb.group({
         ratePlanId: [update?.ratePlanId || '', Validators.required],
         restrictions: this.fb.group({
            rate: [update?.restrictions.rate || null],
            minStayArrival: [update?.restrictions.minStayArrival || null],
            minStayThrough: [update?.restrictions.minStayThrough || null],
            maxStay: [update?.restrictions.maxStay || null],
            closedToArrival: [update?.restrictions.closedToArrival || false],
            closedToDeparture: [update?.restrictions.closedToDeparture || false],
            stopSell: [update?.restrictions.stopSell || false]
         })
      });
   }

   private setWeekDaysDisabledState(enabled: boolean, rangeGroup: UntypedFormGroup): void {
      const activeWeekDaysControl = rangeGroup.get('activeWeekDays');
      if (enabled) {
         activeWeekDaysControl?.enable();
      } else {
         activeWeekDaysControl?.disable();
         activeWeekDaysControl?.setValue(undefined);
      }
   }
}
