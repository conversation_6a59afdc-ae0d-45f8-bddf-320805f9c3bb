.range-card {
  margin-bottom: 16px;
}

.range-settings {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.plan-updates {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
    }
  }
}

.plan-update-card {
  margin-bottom: 12px;
}

.plan-update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.restrictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  align-items: start;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.space-between {
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.add-range {
  margin-bottom: 20px;
}
