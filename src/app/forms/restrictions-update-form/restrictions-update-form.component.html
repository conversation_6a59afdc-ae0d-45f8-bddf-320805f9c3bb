<form [formGroup]="form" class="flex-column no-overflow">
   <div class="flex-column">
      <!-- Date Range Picker -->
      <div class="flex-column add-range">
         <mat-form-field style="height: 0; width: 0; visibility:hidden;">
            <mat-date-range-input [rangePicker]="picker">
               <input [formControl]="start" matStartDate placeholder="Началo">
               <input [formControl]="end" matEndDate placeholder="Край">
            </mat-date-range-input>
            <mat-date-range-picker #picker></mat-date-range-picker>
         </mat-form-field>
         <div>
            <button (click)="picker.open()" color="primary" mat-raised-button>
               <mat-icon>add</mat-icon>
               Добавяне на период
            </button>
         </div>
      </div>

      <!-- Ranges -->
      <div class="ranges" formArrayName="ranges">
         @for (rangeControl of ranges.controls; track rangeControl; let rangeIndex = $index) {
            <mat-card [formGroupName]="rangeIndex" class="range-card" appearance="outlined">
               <mat-card-header>
                  <mat-card-title class="flex-row space-between">
                     <span>{{getDateRange(rangeIndex) | dateRanges}}</span>
                     <button (click)="ranges.removeAt(rangeIndex)" mat-icon-button color="warn">
                        <mat-icon>remove_circle</mat-icon>
                     </button>
                  </mat-card-title>
               </mat-card-header>

               <mat-card-content>
                  <!-- Range Settings -->
                  <div class="range-settings">
                     <mat-slide-toggle formControlName="enableWeekDays">
                        Седмичен филтър
                     </mat-slide-toggle>

                     <mat-form-field>
                        <mat-label>Приоритет</mat-label>
                        <input autocomplete="off" formControlName="priority" matInput type="number">
                     </mat-form-field>

                     <mat-form-field>
                        <mat-label>Активни дни</mat-label>
                        <mat-select formControlName="activeWeekDays" multiple>
                           @for (day of days | keyvalue: originalOrder; track day) {
                              <mat-option [value]="day.key">{{day.value}}</mat-option>
                           }
                        </mat-select>
                     </mat-form-field>
                  </div>

                  <!-- Plan Updates -->
                  <div class="plan-updates">
                     <div class="section-header">
                        <h3>Ограничения за планове</h3>
                        <button (click)="addPlanUpdate(rangeIndex)" mat-icon-button color="primary">
                           <mat-icon>add</mat-icon>
                        </button>
                     </div>

                     <div formArrayName="updates">
                        @for (updateControl of getUpdatesArray(rangeIndex).controls; track updateControl; let updateIndex = $index) {
                           <mat-card [formGroupName]="updateIndex" class="plan-update-card" appearance="outlined">
                              <mat-card-content>
                                 <div class="plan-update-header">
                                    <app-bundle-select
                                       [control]="updateControl.get('ratePlanId')"
                                       [fetchAll]="true"
                                       [wide]="true"
                                       title="Тарифен план"/>
                                    <button (click)="removePlanUpdate(rangeIndex, updateIndex)" mat-icon-button color="warn">
                                       <mat-icon>remove</mat-icon>
                                    </button>
                                 </div>

                                 <div formGroupName="restrictions" class="restrictions-grid">
                                    <mat-form-field>
                                       <mat-label>Тариф</mat-label>
                                       <input autocomplete="off" formControlName="rate" matInput type="number" step="0.01">
                                    </mat-form-field>

                                    <mat-form-field>
                                       <mat-label>Мин. престой при пристигане</mat-label>
                                       <input autocomplete="off" formControlName="minStayArrival" matInput type="number" min="1">
                                    </mat-form-field>

                                    <mat-form-field>
                                       <mat-label>Мин. престой през периода</mat-label>
                                       <input autocomplete="off" formControlName="minStayThrough" matInput type="number" min="1">
                                    </mat-form-field>

                                    <mat-form-field>
                                       <mat-label>Макс. престой</mat-label>
                                       <input autocomplete="off" formControlName="maxStay" matInput type="number" min="1">
                                    </mat-form-field>

                                    <mat-slide-toggle formControlName="closedToArrival">
                                       Затворен за пристигане
                                    </mat-slide-toggle>

                                    <mat-slide-toggle formControlName="closedToDeparture">
                                       Затворен за заминаване
                                    </mat-slide-toggle>

                                    <mat-slide-toggle formControlName="stopSell">
                                       Спрян продажба
                                    </mat-slide-toggle>
                                 </div>
                              </mat-card-content>
                           </mat-card>
                        }
                     </div>
                  </div>
               </mat-card-content>
            </mat-card>
         }
      </div>
   </div>
</form>
