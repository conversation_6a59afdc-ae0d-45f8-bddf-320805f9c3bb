import {inject, Injectable} from '@angular/core';
import {Pricing, RestrictionsUpdate} from '../data/pricing';
import {Observable, of} from 'rxjs';
import {Price} from '../data/price';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {DtoCache} from '../utility/dto-cache';
import {DayOfWeek, Money} from '../data/common';
import {HttpClient} from '@angular/common/http';
import {ConsumableService} from './consumable.service';
import {DateTime} from 'luxon';
import {serverUrl} from '../utility/http-utility';

@Injectable({
   providedIn: 'root'
})
export class PricingService extends DtoCache<Pricing, Pricing> {
   private sConsumable = inject(ConsumableService);

   constructor(http: HttpClient) {
      super('pricing', http);
   }

   getAllPrices(): Observable<Price[]> {
      return this.sConsumable.getAll();
   }

   submitRestrictionsUpdate(update: RestrictionsUpdate): Observable<void> {
      return this.http.post<void>(serverUrl("channel-manager/restrictions-update"), update);
   }

   protected mapToLocal({ranges, ...rest}: PricingDTO): Observable<Pricing> {
      return of({
         ranges: ranges.map(r => {
               const start = DateTime.fromMillis(parseInt(r.dateRange.dateStart, 10));
               const end = DateTime.fromMillis(parseInt(r.dateRange.dateEnd, 10));
               let activeWeekDays;
               if (r.activeWeekDays.length > 0) {
                  activeWeekDays =
                     r.activeWeekDays.map(dayName => dayName.toLowerCase() as DayOfWeek);
               }
               return {
                  dateRange: {start, end},
                  activeWeekDays,
                  priority: r.priority
               };
            }
         ),
         ...rest,
      });
   }

   protected mapToRemote({ranges, ...rest}: MaybeID<Pricing>): MaybeID<PricingDTO> {
      return {
         ranges: ranges.map(r => ({
            dateRange: {
               dateStart: r.dateRange.start.toMillis().toString(),
               dateEnd: r.dateRange.end.toMillis().toString()
            },
            activeWeekDays: r.activeWeekDays ?
               r.activeWeekDays.map((day: DayOfWeek) => day.toUpperCase()) : [],
            priority: r.priority
         })),
         ...rest,
      };
   }
}
